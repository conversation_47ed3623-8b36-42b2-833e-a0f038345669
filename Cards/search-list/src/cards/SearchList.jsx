import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import {
  TextField,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Paper,
  Typography,
  Divider,
  Button,
  makeStyles
} from "@ellucian/react-design-system/core";
import { useUserInfo } from '@ellucian/experience-extension-utils';
import "./styles.css";

import { library } from '@fortawesome/fontawesome-svg-core';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
/*eslint import/namespace: ['error', { allowComputed: true }]*/
import * as Icons from '@fortawesome/free-solid-svg-icons';

// Add all icons to the library
const iconList = Object
  .keys(Icons)
  .filter(key => key !== "fas" && key !== "prefix")
  .map(icon => Icons[icon]);

library.add(...iconList);

const useStyles = makeStyles((theme) => ({
  root: {
    padding: 0,
    height: '100%',
    display: 'flex',
    flexDirection: 'column'
  },
  searchContainer: {
    display: 'flex',
    alignItems: 'center',
    padding: `${theme.spacing(2)} ${theme.spacing(2)} ${theme.spacing(4)} ${theme.spacing(2)}`,
    gap: theme.spacing(2)
  },
  searchField: {
    flexGrow: 1,
    marginLeft: theme.spacing(3)
  },
  categoryFilter: {
    minWidth: 150,
    maxWidth: '50%',
    flexShrink: 0,
    marginRight: theme.spacing(3)
  },
  listContainer: {
    flexGrow: 1,
    overflow: 'auto',
    paddingBottom: theme.spacing(2)
  },
  list: {
    padding: 0,
  },
  listItem: {
    cursor: 'pointer',
    '&:hover': {
      backgroundColor: '#E9E9E9'
    }
  },
  divider: {
    margin: '0'
  },
  noResults: {
    padding: theme.spacing(2),
    textAlign: 'center'
  },
  icon: {
    minWidth: '50px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: '-10px' 
  },
  listItemText: {
    '& .MuiListItemText-primary': {
      fontWeight: 600
    }
  },
  linkButtonsContainer: {
    display: 'flex',
    gap: theme.spacing(1),
    marginTop: theme.spacing(1),
    flexWrap: 'wrap'
  },
  linkButton: {
    minWidth: 'auto',
    padding: `${theme.spacing(0.5)} ${theme.spacing(1.5)}`,
    fontSize: '0.875rem',
    textTransform: 'none'
  },
  multiLinkItem: {
    cursor: 'default',
    '&:hover': {
      backgroundColor: 'transparent'
    }
  }
}));

function SearchList(props) {
  const classes = useStyles();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [items, setItems] = useState([]);
  const [filteredItems, setFilteredItems] = useState([]);
  const [categories, setCategories] = useState(['all']);
  const [iconColor, setIconColor] = useState('#056CF9'); // Default blue color
  const { roles = [] } = useUserInfo() || {};
  
  // Memoize the function with useCallback to prevent unnecessary recreations
  const isItemVisibleToUser = useCallback((item) => {
    // If no audience is specified, show to everyone
    if (!item.audience || item.audience.trim() === '') {
      return true;
    }
    
    // Get all user roles directly
    const userRoles = roles || [];
    
    // Parse item audience - handle comma-separated values
    const itemAudiences = item.audience
      .split(',')
      .map(audience => audience.trim())
      .filter(audience => audience !== '');
    
    // Check if any of the user's roles match any of the item's audience requirements
    const hasMatchingRole = itemAudiences.some(audience => 
      userRoles.includes(audience)
    );
    
    return hasMatchingRole;
  }, [roles]); // Only recreate when roles change
  
  useEffect(() => {
    const customConfig = props?.cardInfo?.configuration?.customConfiguration;

    let foundItems = [];
    let configuredColor = '#056CF9'; // Default blue color

    if (customConfig?.client?.items) {
      foundItems = customConfig.client.items;
      configuredColor = customConfig.client.iconColor || '#056CF9';
    } else if (customConfig?.customConfiguration?.client?.items) {
      foundItems = customConfig.customConfiguration.client.items;
      configuredColor = customConfig.customConfiguration.client.iconColor || '#056CF9';
    } else if (customConfig?.items) {
      foundItems = customConfig.items;
      configuredColor = customConfig.iconColor || '#056CF9';
    }

    // Set the icon color
    setIconColor(configuredColor);

    if (Array.isArray(foundItems) && foundItems.length > 0) {
      // Filter items based on user's audience
      const visibleItems = foundItems.filter(isItemVisibleToUser);
      setItems(visibleItems);

      // Extract unique categories
      const allCategories = visibleItems
        .map(item => item.category)
        .filter(category => category && category.trim() !== '');

      const uniqueCategories = ['all', ...new Set(allCategories)];
      setCategories(uniqueCategories);
    } else {
      setItems([]);
      setCategories(['all']);
    }
  }, [props, roles, isItemVisibleToUser]);
  
  useEffect(() => {
    // Filter items based on search term and category
    const filtered = items.filter(item => {
      if (!item || (!item.title && !item.description)) return false;
      
      // Category filter
      if (selectedCategory !== 'all' && item.category !== selectedCategory) {
        return false;
      }
      
      // Search term filter
      const searchLower = searchTerm.toLowerCase();
      return (
        (item.title && item.title.toLowerCase().includes(searchLower)) ||
        (item.description && item.description.toLowerCase().includes(searchLower)) ||
        (item.keywords && item.keywords.toLowerCase().includes(searchLower))
      );
    });
    
    setFilteredItems(filtered);
  }, [items, searchTerm, selectedCategory]);
  
  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };
  
  const handleCategoryChange = (event) => {
    setSelectedCategory(event.target.value);
  };
  
  const handleItemClick = (url) => {
    if (url) {
      window.open(url, '_blank');
    }
  };

  const handleLinkClick = (url, event) => {
    event.stopPropagation(); // Prevent row click when button is clicked
    if (url) {
      window.open(url, '_blank');
    }
  };

  // Helper function to determine if item has multiple links
  const hasMultipleLinks = (item) => {
    return item.links && Array.isArray(item.links) && item.links.length >= 2;
  };

  // Helper function to get the primary URL for single-link items
  const getPrimaryUrl = (item) => {
    if (item.links && Array.isArray(item.links) && item.links.length === 1) {
      return item.links[0].url;
    }
    // Fallback to url field for backward compatibility
    return item.url;
  };

  // Helper function to determine if item should be clickable (has exactly one link)
  const isClickableItem = (item) => {
    if (item.links && Array.isArray(item.links)) {
      return item.links.length === 1;
    }
    // Fallback to url field for backward compatibility
    return !!item.url;
  };
  
  // Simplified render to isolate the issue
  return (
    <Paper className={classes.root}>
      <div className={classes.searchContainer}>
        <TextField
          className={classes.searchField}
          label="Search"
          variant="outlined"
          value={searchTerm}
          onChange={handleSearchChange}
          placeholder="Type to search..."
          size="small"
        />
        
        {categories.length > 1 && (
          <div className={classes.categoryFilter}>
            <select
              value={selectedCategory}
              onChange={handleCategoryChange}
              style={{
                padding: '8px',
                borderRadius: '4px',
                fontSize: '14px',
                border: '1px solid #b2b3b7',
                backgroundColor: 'white',
                width: '100%',
                height: '38px' // Match the height of the TextField
              }}
            >
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category === 'all' ? 'All Categories' : category}
                </option>
              ))}
            </select>
          </div>
        )}
      </div>
      
      <div className={classes.listContainer}>
        {filteredItems.length > 0 ? (
          <List className={classes.list}>
            {filteredItems.map((item, index) => {
              const isMultiLink = hasMultipleLinks(item);
              const isClickable = isClickableItem(item);
              const primaryUrl = getPrimaryUrl(item);

              return (
                <React.Fragment key={index}>
                  <ListItem
                    className={isMultiLink ? classes.multiLinkItem : classes.listItem}
                    onClick={isClickable && !isMultiLink ? () => handleItemClick(primaryUrl) : undefined}
                    style={{ flexDirection: 'column', alignItems: 'stretch' }}
                  >
                    {/* Top row with icon, title and description */}
                    <div style={{ display: 'flex', width: '100%' }}>
                      {/* Icon */}
                      <ListItemIcon className={classes.icon}>
                        {item.icon ? (
                          <FontAwesomeIcon icon={item.icon} style={{ color: iconColor, fontSize: '1.1em' }} />
                        ) : (
                          <FontAwesomeIcon icon="link" style={{ color: iconColor, fontSize: '1.1em' }} />
                        )}
                      </ListItemIcon>

                      {/* Title and Description */}
                      <ListItemText
                        className={classes.listItemText}
                        primary={item.title || ''}
                        secondary={item.description || ''}
                        style={{ flex: 1 }}
                      />
                    </div>

                    {/* Link Buttons for multiple links - below description */}
                    {isMultiLink && (
                      <div className={classes.linkButtonsContainer} style={{ marginLeft: '40px',  marginTop: '10px', gap: '10px' }}>
                        {item.links.map((link, linkIndex) => {
                          // First button uses icon color, subsequent buttons are black
                          const backgroundColor = linkIndex === 0 ? iconColor : '#000000';

                          return (
                            <Button
                              key={linkIndex}
                              className={classes.linkButton}
                              variant="contained"
                              size="small"
                              onClick={(event) => handleLinkClick(link.url, event)}
                              style={{
                                backgroundColor: backgroundColor,
                                color: 'white',
                                paddingLeft: '15px',  // Add padding to the left
                                paddingRight: '15px',  // Add padding to the right
                                '&:hover': {
                                  backgroundColor: "#ffffff",
                                  opacity: 0.8,
                                  color: "#000000"
                                }
                              }}
                            >
                              {link.label || `Link ${linkIndex + 1}`}
                            </Button>
                          );
                        })}
                      </div>
                    )}
                  </ListItem>
                  {index < filteredItems.length - 1 && <Divider />}
                </React.Fragment>
              );
            })}
          </List>
        ) : (
          <Typography className={classes.noResults} variant="body1">
            {searchTerm || selectedCategory !== 'all' ? "No results found" : "No items available"}
          </Typography>
        )}
      </div>
    </Paper>
  );
}

SearchList.propTypes = {
  cardInfo: PropTypes.shape({
    configuration: PropTypes.shape({
      customConfiguration: PropTypes.object
    })
  })
};

export default SearchList;
